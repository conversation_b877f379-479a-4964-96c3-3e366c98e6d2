import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Fade,
  Chip,
  Button,
  Tooltip,
  Card,
  CardContent,
  Divider
} from '@mui/material';
import {
  Lightbulb as LightbulbIcon,
  AutoAwesome as AutoAwesomeIcon,
  Psychology as PsychologyIcon,
  Refresh as RefreshIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';
import { styled, keyframes } from '@mui/material/styles';
import api from '../../services/api';

// Animations
const glow = keyframes`
  0% {
    box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.6);
  }
  100% {
    box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
  }
`;

const slideIn = keyframes`
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
`;

// Styled components with project theme colors
const AssistantContainer = styled(Card)(({ theme, isVisible }) => ({
  position: 'fixed',
  top: theme.spacing(10),
  right: theme.spacing(3),
  width: '350px',
  maxWidth: '90vw',
  zIndex: 1200,
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  borderRadius: theme.spacing(3),
  border: '2px solid rgba(255, 255, 255, 0.2)',
  backdropFilter: 'blur(10px)',
  animation: `${slideIn} 0.5s ease-out`,
  transition: 'all 0.3s ease',
  transform: isVisible ? 'translateX(0)' : 'translateX(100%)',
  opacity: isVisible ? 1 : 0,
  '&:hover': {
    animation: `${glow} 2s infinite`,
  }
}));

const AssistantHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: theme.spacing(2),
  background: 'rgba(255, 255, 255, 0.1)',
  borderRadius: `${theme.spacing(3)} ${theme.spacing(3)} 0 0`,
  borderBottom: '1px solid rgba(255, 255, 255, 0.2)'
}));

const SuggestionContent = styled(CardContent)(({ theme }) => ({
  padding: theme.spacing(2),
  color: 'white',
  '& .MuiTypography-root': {
    color: 'white',
    textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)',
    lineHeight: 1.6
  }
}));

const ActionButtons = styled(Box)(({ theme }) => ({
  display: 'flex',
  gap: theme.spacing(1),
  padding: theme.spacing(2),
  paddingTop: 0,
  justifyContent: 'flex-end'
}));

const StatusIndicator = styled(Box)(({ theme, status }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  padding: theme.spacing(1, 2),
  borderRadius: theme.spacing(1),
  backgroundColor: status === 'thinking' ? 'rgba(255, 193, 7, 0.2)' : 
                   status === 'ready' ? 'rgba(76, 175, 80, 0.2)' : 
                   'rgba(158, 158, 158, 0.2)',
  border: `1px solid ${status === 'thinking' ? '#FFC107' : 
                       status === 'ready' ? '#4CAF50' : 
                       '#9E9E9E'}`,
  '& .MuiTypography-root': {
    fontSize: '0.75rem',
    fontWeight: 'bold',
    color: status === 'thinking' ? '#FFC107' : 
           status === 'ready' ? '#4CAF50' : 
           '#9E9E9E'
  }
}));

const PersistentAIAssistant = ({ 
  text, 
  cursorPosition, 
  ageGroup = 'late_primary',
  isEnabled = true,
  userPreferences = {}
}) => {
  const [currentSuggestion, setCurrentSuggestion] = useState(null);
  const [isVisible, setIsVisible] = useState(true);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [status, setStatus] = useState('ready'); // 'ready', 'thinking', 'idle'
  const [lastAnalyzedText, setLastAnalyzedText] = useState('');
  const [analysisCount, setAnalysisCount] = useState(0);
  
  const timeoutRef = useRef(null);
  const lastAnalysisTime = useRef(0);
  const sessionId = useRef(Math.random().toString(36).substr(2, 9));

  // Debounced analysis function
  const debouncedAnalyze = useCallback(async () => {
    if (!isEnabled || !text || text.length < 15) {
      setCurrentSuggestion(null);
      setStatus('idle');
      return;
    }

    // Avoid analyzing the same text repeatedly
    if (text === lastAnalyzedText) {
      return;
    }

    // Rate limiting: max 1 analysis per 3 seconds
    const now = Date.now();
    if (now - lastAnalysisTime.current < 3000) {
      return;
    }

    setIsAnalyzing(true);
    setStatus('thinking');
    lastAnalysisTime.current = now;
    setAnalysisCount(prev => prev + 1);

    try {
      const response = await api.post('/realtime/suggestions', {
        text,
        cursor_position: cursorPosition,
        age_group: ageGroup,
        user_preferences: {
          ...userPreferences,
          analysis_count: analysisCount,
          session_id: sessionId.current
        }
      });

      const { suggestions, should_show } = response.data;
      
      if (should_show && suggestions.length > 0) {
        setCurrentSuggestion(suggestions[0]);
        setStatus('ready');
      } else {
        setCurrentSuggestion(null);
        setStatus('idle');
      }
      
      setLastAnalyzedText(text);

      console.log('🤖 AI suggestion generated:', {
        suggestion: suggestions[0]?.message,
        analysis_time: response.data.analysis_time_ms
      });

    } catch (error) {
      console.error('❌ AI suggestion error:', error);
      setCurrentSuggestion(null);
      setStatus('idle');
    } finally {
      setIsAnalyzing(false);
    }
  }, [text, cursorPosition, ageGroup, isEnabled, userPreferences, lastAnalyzedText, analysisCount]);

  // Set up debounced analysis
  useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Debounce delay based on text length
    const debounceDelay = text.length > 100 ? 4000 : 3000;

    timeoutRef.current = setTimeout(() => {
      debouncedAnalyze();
    }, debounceDelay);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [text, debouncedAnalyze]);

  const handleRefresh = () => {
    setLastAnalyzedText(''); // Force re-analysis
    debouncedAnalyze();
  };

  const toggleVisibility = () => {
    setIsVisible(!isVisible);
  };

  const getStatusText = () => {
    switch (status) {
      case 'thinking':
        return 'AI is thinking...';
      case 'ready':
        return 'Suggestion ready';
      case 'idle':
        return 'Waiting for content';
      default:
        return 'Ready';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'thinking':
        return <AutoAwesomeIcon sx={{ fontSize: '1rem', animation: 'spin 1s linear infinite' }} />;
      case 'ready':
        return <PsychologyIcon sx={{ fontSize: '1rem' }} />;
      default:
        return <LightbulbIcon sx={{ fontSize: '1rem' }} />;
    }
  };

  if (!isEnabled) {
    return null;
  }

  return (
    <>
      {/* Toggle button when hidden */}
      {!isVisible && (
        <Tooltip title="Show AI Writing Assistant">
          <IconButton
            onClick={toggleVisibility}
            sx={{
              position: 'fixed',
              top: 80,
              right: 16,
              zIndex: 1201,
              backgroundColor: 'primary.main',
              color: 'white',
              '&:hover': {
                backgroundColor: 'primary.dark',
              }
            }}
          >
            <VisibilityIcon />
          </IconButton>
        </Tooltip>
      )}

      <AssistantContainer isVisible={isVisible}>
        <AssistantHeader>
          <Box display="flex" alignItems="center" gap={1}>
            <LightbulbIcon sx={{ color: '#FCD34D', fontSize: '1.5rem' }} />
            <Typography variant="h6" fontWeight="bold" color="white">
              AI Writing Assistant
            </Typography>
          </Box>
          <Box display="flex" alignItems="center" gap={1}>
            <Tooltip title="Refresh suggestion">
              <IconButton 
                size="small" 
                onClick={handleRefresh}
                disabled={isAnalyzing}
                sx={{ color: 'white', opacity: 0.8 }}
              >
                <RefreshIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title="Hide assistant">
              <IconButton 
                size="small" 
                onClick={toggleVisibility}
                sx={{ color: 'white', opacity: 0.8 }}
              >
                <VisibilityOffIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        </AssistantHeader>

        <SuggestionContent>
          <StatusIndicator status={status}>
            {getStatusIcon()}
            <Typography variant="caption">
              {getStatusText()}
            </Typography>
          </StatusIndicator>

          <Divider sx={{ my: 2, borderColor: 'rgba(255, 255, 255, 0.2)' }} />

          {currentSuggestion ? (
            <Box>
              <Typography variant="body1" sx={{ 
                fontSize: '1rem',
                fontWeight: 500,
                marginBottom: 1
              }}>
                💡 Writing Tip:
              </Typography>
              <Typography variant="body2" sx={{ 
                fontSize: '0.95rem',
                lineHeight: 1.6,
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                padding: 2,
                borderRadius: 2,
                border: '1px solid rgba(255, 255, 255, 0.2)'
              }}>
                {currentSuggestion.message}
              </Typography>
              
              <Chip 
                label={`Age: ${ageGroup.replace('_', ' ')}`}
                size="small"
                sx={{ 
                  mt: 1,
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  fontSize: '0.7rem'
                }}
              />
            </Box>
          ) : (
            <Box textAlign="center" py={2}>
              <Typography variant="body2" sx={{ 
                opacity: 0.8,
                fontStyle: 'italic'
              }}>
                {text.length < 15 
                  ? "Start writing to get AI suggestions..." 
                  : "Keep writing for more suggestions!"}
              </Typography>
            </Box>
          )}
        </SuggestionContent>
      </AssistantContainer>
    </>
  );
};

export default PersistentAIAssistant;
