import React, { useState } from 'react';
import {
  Container,
  <PERSON>rid,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Box,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  CardMedia,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Folder as FolderIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';

import projectService from '../../services/projectService';

const Dashboard = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm();

  // Fetch projects
  const { data: projects, isLoading } = useQuery(
    'projects',
    projectService.getProjects,
    {
      onError: (error) => {
        toast.error('Failed to load projects');
      },
    }
  );

  // Create project mutation
  const createProjectMutation = useMutation(
    projectService.createProject,
    {
      onSuccess: () => {
        queryClient.invalidateQueries('projects');
        setCreateDialogOpen(false);
        reset();
        toast.success('Project created successfully!');
      },
      onError: (error) => {
        toast.error('Failed to create project');
      },
    }
  );

  // Delete project mutation
  const deleteProjectMutation = useMutation(
    projectService.deleteProject,
    {
      onSuccess: () => {
        queryClient.invalidateQueries('projects');
        toast.success('Project deleted successfully!');
      },
      onError: (error) => {
        toast.error('Failed to delete project');
      },
    }
  );

  const handleCreateProject = (data) => {
    createProjectMutation.mutate(data);
  };

  const handleQuickStart = () => {
    const quickProjectData = {
      name: `My Story ${new Date().toLocaleDateString()}`,
      description: 'A new writing project'
    };

    createProjectMutation.mutate(quickProjectData, {
      onSuccess: (newProject) => {
        // Create first document and navigate directly to editor
        const firstDocData = {
          title: 'Chapter 1',
          document_type: 'chapter',
          project_id: newProject.id,
          order_index: 0
        };

        projectService.createDocument(firstDocData).then((newDoc) => {
          navigate(`/document/${newDoc.id}`);
          toast.success('Quick start project created! Start writing now!');
        }).catch(() => {
          // If document creation fails, just go to project view
          navigate(`/project/${newProject.id}`);
          toast.success('Project created! Add your first document to start writing.');
        });
      }
    });
  };

  const handleDeleteProject = (projectId) => {
    if (window.confirm('Are you sure you want to delete this project?')) {
      deleteProjectMutation.mutate(projectId);
    }
  };

  const handleOpenProject = (projectId) => {
    navigate(`/project/${projectId}`);
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          My Projects
        </Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleQuickStart}
            disabled={createProjectMutation.isLoading}
            sx={{
              background: 'linear-gradient(135deg, #FCD34D 0%, #F59E0B 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #F59E0B 0%, #D97706 100%)',
              }
            }}
          >
            Quick Start
          </Button>
          <Button
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={() => setCreateDialogOpen(true)}
          >
            New Project
          </Button>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {projects?.map((project) => (
          <Grid item xs={12} sm={6} md={4} key={project.id}>
            <Card 
              sx={{ 
                height: '100%', 
                display: 'flex', 
                flexDirection: 'column',
                cursor: 'pointer',
                '&:hover': {
                  boxShadow: 4,
                },
              }}
              onClick={() => handleOpenProject(project.id)}
            >
              {project.cover_image ? (
                <CardMedia
                  component="img"
                  height="200"
                  image={project.cover_image}
                  alt={project.name}
                />
              ) : (
                <Box
                  sx={{
                    height: 200,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: 'grey.200',
                  }}
                >
                  <FolderIcon sx={{ fontSize: 60, color: 'grey.500' }} />
                </Box>
              )}
              
              <CardContent sx={{ flexGrow: 1 }}>
                <Typography gutterBottom variant="h6" component="h2">
                  {project.name}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {project.description || 'No description'}
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  Created: {new Date(project.created_at).toLocaleDateString()}
                </Typography>
              </CardContent>
              
              <CardActions>
                <Button
                  size="small"
                  startIcon={<EditIcon />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleOpenProject(project.id);
                  }}
                >
                  Open
                </Button>
                <Button
                  size="small"
                  color="error"
                  startIcon={<DeleteIcon />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteProject(project.id);
                  }}
                >
                  Delete
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {projects?.length === 0 && (
        <Box textAlign="center" mt={8}>
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No projects yet
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            Create your first project to get started with your writing journey!
          </Typography>
          <Box display="flex" justifyContent="center" gap={2}>
            <Button
              variant="contained"
              size="large"
              startIcon={<AddIcon />}
              onClick={handleQuickStart}
              disabled={createProjectMutation.isLoading}
              sx={{
                background: 'linear-gradient(135deg, #FCD34D 0%, #F59E0B 100%)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #F59E0B 0%, #D97706 100%)',
                }
              }}
            >
              Quick Start Writing
            </Button>
            <Button
              variant="outlined"
              size="large"
              startIcon={<AddIcon />}
              onClick={() => setCreateDialogOpen(true)}
            >
              Create Custom Project
            </Button>
          </Box>
        </Box>
      )}

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
        }}
        onClick={() => setCreateDialogOpen(true)}
      >
        <AddIcon />
      </Fab>

      {/* Create Project Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="sm"
        fullWidth
        TransitionComponent={undefined}
        transitionDuration={0}
      >
        <DialogTitle>Create New Project</DialogTitle>
        <form onSubmit={handleSubmit(handleCreateProject)}>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              label="Project Name"
              fullWidth
              variant="outlined"
              {...register('name', { required: 'Project name is required' })}
              error={!!errors.name}
              helperText={errors.name?.message}
            />
            <TextField
              margin="dense"
              label="Description"
              fullWidth
              multiline
              rows={3}
              variant="outlined"
              {...register('description')}
              sx={{ mt: 2 }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
            <Button 
              type="submit" 
              variant="contained"
              disabled={createProjectMutation.isLoading}
            >
              {createProjectMutation.isLoading ? <CircularProgress size={24} /> : 'Create'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </Container>
  );
};

export default Dashboard;
